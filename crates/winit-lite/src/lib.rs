// MIT/Apache2 License

//! Portable interface to winuser, AppKit and Wayland.

#![forbid(unsafe_code)]

#[cfg(windows)]
pub mod windows;
#[cfg(windows)]
use windows as sys;

#[cfg(not(any(windows, target_os = "macos")))]
pub mod wayland;
#[cfg(not(any(windows, target_os = "macos")))]
use wayland as sys;

use raw_window_handle::{DisplayHandle, HandleError, HasDisplayHandle, HasWindowHandle, WindowHandle};

use std::borrow::Borrow;
use std::cell::Cell;
use std::io;
use std::marker::PhantomData;
use std::mem;
use std::time::{Duration, Instant};

pub use raw_window_handle;

//
// Event handling
//

/// Event handler.
pub trait EventHandler<'f>: 'f {
    /// Type of the underlying display.
    type Display: Borrow<Display<'f>>;

    /// We have been woken up.
    fn wakeup(&mut self, display: &Self::Display, cause: WakeupCause);

    /// We are about to wait.
    fn about_to_wait(&mut self, display: &Self::Display);

    /// The user woke up the `Waker`.
    fn user(&mut self, display: &Self::Display);

    /// A window should be redrawn.
    fn redraw_requested(&mut self, display: &Self::Display, window_id: WindowId);
}

/// Event sum type.
#[non_exhaustive]
pub enum Event {
    /// We have been woken up.
    Wakeup(WakeupCause),

    /// We are about to wait.
    AboutToWait,

    /// The user woke up the `Waker`.
    User,

    /// A window should be redrawn.
    RedrawRequested(WindowId),
}

/// A wrapper around a closure that handles events.
pub struct Closure<D, F> {
    /// Closure to call.
    closure: F,

    /// Not used.
    _eat: PhantomData<D>,
}

impl<D, F> From<F> for Closure<D, F> {
    #[inline]
    fn from(closure: F) -> Self {
        Self {
            closure,
            _eat: PhantomData,
        }
    }
}

impl<'f, D, F> EventHandler<'f> for Closure<D, F>
where
    D: Borrow<Display<'f>> + 'f,
    F: FnMut(&D, Event) + 'f,
{
    type Display = D;

    #[inline]
    fn wakeup(&mut self, display: &D, cause: WakeupCause) {
        (self.closure)(display, Event::Wakeup(cause));
    }

    #[inline]
    fn about_to_wait(&mut self, display: &D) {
        (self.closure)(display, Event::AboutToWait);
    }

    #[inline]
    fn user(&mut self, display: &D) {
        (self.closure)(display, Event::User);
    }

    #[inline]
    fn redraw_requested(&mut self, display: &D, window_id: WindowId) {
        (self.closure)(display, Event::RedrawRequested(window_id));
    }
}

/// Cause of the wakeup.
#[derive(Debug, Clone, Copy)]
pub enum WakeupCause {
    /// First-time initialization.
    Init,

    /// The wait has been cancelled by receiving an event.
    WaitCancelled,

    /// The wait has timed out.
    WaitTimedOut,

    /// Next poll.
    Poll,
}

//
// Window attributes
//

/// Size of a window.
#[derive(Debug, Copy, Clone)]
pub enum Size {
    /// Size in terms of physical pixels.
    Physical { width: u32, height: u32 },

    /// Size in terms of logical pixels.
    Logical { width: f64, height: f64 },
}

/// Position of a window.
#[derive(Debug, Copy, Clone)]
pub enum Position {
    /// Position in terms of physical pixels.
    Physical { x: i32, y: i32 },

    /// Position in terms of logical pixels.
    Logical { x: f64, y: f64 },
}

/// Set up window attributes.
macro_rules! window_attrs {
    ($lt: lifetime: $(
        $(#[$meta:meta])*
        $fname: ident : [$borrowed_ty: ty, $owned_ty: ty]
    ),*) => {
        /// Window attribute change set.
        #[derive(Default)]
        pub struct WindowAttributes<$lt> {
            $(
                $(#[$meta])*
                pub $fname: Option<$borrowed_ty>,
            )*
        }
    }
}

window_attrs! {
    'f:

    /// Position of the window.
    position: [Position, Position],

    /// Size of the window.
    size: [Size, Size],

    /// Title of the window.
    title: [&'f str, String]
}

//
// Event polling
//

/// Interface to the GUI system.
pub struct Display<'f> {
    /// Inner display.
    inner: sys::Display<'f>,

    /// Event loop state.
    state: Cell<EventLoopState>,
}

/// Owned display handle.
pub struct OwnedDisplayHandle {
    /// Inner display.
    inner: sys::OwnedDisplayHandle,
}

/// Builder for a [`Poller`].
#[derive(Default)]
pub struct DisplayBuilder {}

/// Unique identifier for a window.
#[derive(Debug, Copy, Clone, PartialEq, Eq, PartialOrd, Ord, Hash)]
pub struct WindowId(pub(crate) usize);

/// A window that can be drawn to.
pub struct Window<'f> {
    /// Window ID.
    id: WindowId,

    /// Reference to the display.
    display: &'f Display<'f>,
}

impl<'f> Window<'f> {
    /// Get the window ID.
    #[inline]
    pub fn id(&self) -> WindowId {
        self.id
    }

    /// Request a redraw for this window.
    #[inline]
    pub fn request_redraw(&self) -> io::Result<()> {
        self.display.request_redraw(self.id)
    }
}

impl<'f> Drop for Window<'f> {
    fn drop(&mut self) {
        // Destroy the window when dropped
        let _ = self.display.inner.destroy_window(self.id);
    }
}

impl<'f> HasWindowHandle for Window<'f> {
    #[inline]
    fn window_handle(&self) -> Result<WindowHandle<'_>, HandleError> {
        self.display.inner.window_handle(self.id)
    }
}

/// Run with an event handler.
pub fn run<'f, H: EventHandler<'f> + 'f>(display: H::Display, handler: H) -> io::Result<()> {
    sys::run(display, handler)
}

impl<'f> Display<'f> {
    /// Create a new display.
    pub fn new() -> io::Result<Self> {
        DisplayBuilder::new().build()
    }
}

impl Display<'_> {
    /// Indicate we should enter polling mode.
    #[inline]
    pub fn set_poll(&self) {
        self.state.set(EventLoopState::Poll);
    }

    /// Indicate we should wait forever.
    #[inline]
    pub fn set_wait(&self) {
        self.state.set(EventLoopState::Wait);
    }

    /// Indicate we should wait for a timeout.
    #[inline]
    pub fn set_timeout(&self, timeout: Duration) {
        match Instant::now().checked_add(timeout) {
            Some(timeout) => self.set_deadline(timeout),
            None => self.set_wait(),
        }
    }

    /// Indicate we should wait for a deadline.
    #[inline]
    pub fn set_deadline(&self, deadline: Instant) {
        self.state.set(EventLoopState::Timeout(deadline));
    }

    /// Indicate that we should exit as soon as possible.
    #[inline]
    pub fn exit(&self) {
        self.state.set(EventLoopState::Exit);
    }
}

impl Display<'_> {
    /// Create a new window.
    #[inline]
    pub fn create_window(&self, attrs: WindowAttributes<'_>) -> io::Result<WindowId> {
        self.inner.create_window(attrs)
    }

    /// Request a redraw for the specified window.
    #[inline]
    pub fn request_redraw(&self, window_id: WindowId) -> io::Result<()> {
        self.inner.request_redraw(window_id)
    }

    /// Create an owned display handle for GUI systems.
    #[inline]
    pub fn create_handle(&self) -> OwnedDisplayHandle {
        OwnedDisplayHandle {
            inner: self.inner.create_handle(),
        }
    }
}

impl DisplayBuilder {
    /// Create a new display builder.
    pub fn new() -> Self {
        Self::default()
    }

    /// Create a new display.
    pub fn build<'f>(self) -> io::Result<Display<'f>> {
        let display = sys::Display::new()?;
        Ok(Display {
            inner: display,
            state: EventLoopState::Wait.into(),
        })
    }
}

impl HasDisplayHandle for Display<'_> {
    #[inline]
    fn display_handle(&self) -> Result<DisplayHandle<'_>, HandleError> {
        self.inner.display_handle()
    }
}

impl HasDisplayHandle for OwnedDisplayHandle {
    #[inline]
    fn display_handle(&self) -> Result<DisplayHandle<'_>, HandleError> {
        self.inner.display_handle()
    }
}

//
// Utilities
//

/// Current event loop state.
#[derive(Debug, Copy, Clone)]
enum EventLoopState {
    /// Poll mode; don't bother waiting.
    Poll,

    /// Wait forever for events.
    Wait,

    /// Wait for a timeout.
    Timeout(Instant),

    /// Exit as soon as possible.
    Exit,
}

impl EventLoopState {
    /// Get the amount of time to wait.
    fn duration(&self) -> Option<Duration> {
        match self {
            Self::Poll => Some(Duration::ZERO),
            Self::Wait => None,
            Self::Timeout(timeout) => timeout.checked_duration_since(Instant::now()),
            Self::Exit => None,
        }
    }
}

/// Implementation of a simple arena.
///
/// Useful for defining window containers.
struct Slab<T> {
    /// Chunk of filled memory.
    entries: Vec<SlabEntry<T>>,

    /// Number of filled entries.
    filled: usize,

    /// Index of the first vacant entry.
    first_vacant: usize,
}

/// Entry in a `Slab`.
enum SlabEntry<T> {
    /// Filled entry.
    Filled(T),

    /// Vacant entry.
    ///
    /// Points to the next vacant entry.
    Vacant(usize),
}

impl<T> Slab<T> {
    /// Create a new slab.
    fn new() -> Self {
        Self {
            entries: Vec::new(),
            filled: 0,
            first_vacant: 0,
        }
    }

    /// Get the entry at the given key.
    fn get(&self, key: usize) -> Option<&T> {
        match self.entries.get(key) {
            Some(SlabEntry::Filled(value)) => Some(value),
            _ => None,
        }
    }

    /// Get a mutable reference to the entry at the given key.
    fn get_mut(&mut self, key: usize) -> Option<&mut T> {
        match self.entries.get_mut(key) {
            Some(SlabEntry::Filled(value)) => Some(value),
            _ => None,
        }
    }

    /// Insert a new entry.
    fn insert(&mut self, value: T) -> usize {
        let key = self.first_vacant;
        self.filled += 1;

        if key == self.entries.len() {
            // Expand the vector.
            self.entries.push(SlabEntry::Filled(value));
            self.first_vacant = key + 1;
        } else {
            self.first_vacant = match self.entries.get(key) {
                Some(SlabEntry::Vacant(next)) => *next,
                _ => unreachable!(),
            };
            self.entries[key] = SlabEntry::Filled(value);
        }

        key
    }

    /// Remove an entry.
    fn remove(&mut self, key: usize) -> Option<T> {
        let entry = self.entries.get_mut(key)?;

        // Swap the entry out.
        let prev = mem::replace(entry, SlabEntry::Vacant(self.first_vacant));
        if let SlabEntry::Filled(val) = prev {
            self.filled -= 1;
            self.first_vacant = key;
            Some(val)
        } else {
            None
        }
    }
}

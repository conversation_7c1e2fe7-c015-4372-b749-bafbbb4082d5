// MIT/Apache2 License

use std::borrow::<PERSON><PERSON>;
use std::io;

use softbuffer_lite::Context;
use winit_lite::{Closure, Display, Event, WakeupCause, WindowAttributes};

struct DisplayWrapper<'f>(Context<Display<'f>>);

impl<'f> <PERSON><PERSON><Display<'f>> for DisplayWrapper<'f> {
    fn borrow(&self) -> &Display<'f> {
        self.0.as_provider()
    }
}

fn main() -> io::Result<()> {
    let display = DisplayWrapper(Context::new(Display::new()?)?);
    let mut redraw_count = 0;

    winit_lite::run(
        display,
        Closure::from(move |display: &DisplayWrapper<'_>, event| match event {
            Event::Wakeup(cause) => {
                let display = display.0.as_provider();
                if let WakeupCause::Init = cause {
                    // Create a window and request an initial redraw
                    let id = display.create_window(WindowAttributes::default()).unwrap();
                    display.request_redraw(id).unwrap();
                    println!("Window created with ID: {id:?}");
                } else if let WakeupCause::WaitTimedOut = cause {
                    display.exit();
                }
            }
            Event::RedrawRequested(id) => {
                redraw_count += 1;
                println!("Redraw requested for window {id:?} (count: {redraw_count})");

                // Request another redraw after a few redraws to demonstrate the functionality
                if redraw_count < 3 {
                    display.0.as_provider().request_redraw(id).unwrap();
                } else if redraw_count == 3 {
                    // Set a timeout to exit after demonstrating redraw events
                    display.0.as_provider().set_timeout(std::time::Duration::from_secs(2));
                }
            }
            _ => {}
        }),
    )
}

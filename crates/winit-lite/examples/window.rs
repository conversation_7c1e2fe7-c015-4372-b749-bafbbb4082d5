// MIT/Apache2 License

use std::borrow::<PERSON><PERSON>;
use std::io;

use softbuffer_lite::{Context, Rectangle};
use winit_lite::{Closure, Display, Event, WakeupCause, WindowAttributes};

struct DisplayWrapper<'f>(Context<Display<'f>>);

impl<'f> Borrow<Display<'f>> for DisplayWrapper<'f> {
    fn borrow(&self) -> &Display<'f> {
        self.0.as_provider()
    }
}

fn main() -> io::Result<()> {
    let display = DisplayWrapper(Context::new(Display::new()?)?);
    let mut window = None;

    winit_lite::run(
        display,
        Closure::from(move |display: &DisplayWrapper<'_>, event| match event {
            Event::Wakeup(cause) => {
                let display = display.0.as_provider();
                if let WakeupCause::Init = cause {
                    window = Some(display.create_window(WindowAttributes::default()).unwrap());
                    display.set_timeout(std::time::Duration::from_secs(2));
                } else if let WakeupCause::WaitTimedOut = cause {
                    display.exit();
                }
            }
            _ => {}
        }),
    )
}

// MIT/Apache2 License

//! Wayland example for softbuffer-lite using landway.

use landway::protocol::{
    wayland as wl, xdg_decoration_unstable_v1 as xdg_decoration, xdg_shell as xdgs,
};
use landway::{Display, EventQueue};
use softbuffer_lite::{Context, Rectangle};
use std::cell::RefCell;
use std::ffi::CStr;
use std::io;
use std::mem::MaybeUninit;
use std::num::NonZeroU32;
use std::rc::Rc;

/// Global Wayland object.
#[derive(Debug)]
struct Global {
    /// ID of the global.
    name: u32,

    /// Interface for the global.
    interface: String,

    /// Version of the global.
    version: u32,
}

/// Registry state.
struct Registry {
    /// Registry proxy.
    proxy: wl::WlRegistry,

    /// Globals found by the registry.
    globals: Vec<Global>,
}

impl Registry {
    /// Bind a global in the registry.
    fn bind<I>(
        &self,
        interface: &'static landway::Interface,
        version: u32,
        queue: &EventQueue,
    ) -> io::Result<I>
    where
        landway::Proxy: Into<I>,
    {
        // Find the global.
        let global = self
            .globals
            .iter()
            .find(|global| global.interface == interface.name().to_string_lossy());

        let (name, available_version) = match global {
            Some(global) => (global.name, global.version),
            None => {
                return Err(io::Error::new(
                    io::ErrorKind::NotFound,
                    format!("global {:?} not found", interface.name()),
                ));
            }
        };

        // Use the minimum of requested and available version
        let bind_version = version.min(available_version);

        // Perform the bind.
        let proxy = self.proxy.bind(name, interface, bind_version, queue)?;
        Ok(proxy.into())
    }

    /// Try to bind a global in the registry, returning None if not found.
    fn try_bind<I>(
        &self,
        interface: &'static landway::Interface,
        version: u32,
        queue: &EventQueue,
    ) -> Option<I>
    where
        landway::Proxy: Into<I>,
    {
        self.bind(interface, version, queue).ok()
    }
}

fn main() -> io::Result<()> {
    // Connect to the Wayland display
    let display = Display::connect()?;
    let mut queue = display.create_queue(None)?;

    // Get the registry to find global objects
    let display_proxy = display.display_proxy();
    let registry_proxy = display_proxy.get_registry(&queue)?;

    // Set up registry state
    let registry_state = Rc::new(RefCell::new(Registry {
        proxy: registry_proxy,
        globals: Vec::new(),
    }));

    // Set up registry listener to collect globals
    registry_state.borrow().proxy.add_listener({
        let registry_state = registry_state.clone();
        move |_, event| {
            let mut registry = registry_state.borrow_mut();
            match event {
                wl::wl_registry::Event::Global {
                    name,
                    interface,
                    version,
                } => {
                    registry.globals.push(Global {
                        name,
                        interface: interface.to_string_lossy().into_owned(),
                        version,
                    });
                }
                wl::wl_registry::Event::GlobalRemove { name } => {
                    registry.globals.retain(|global| global.name != name);
                }
                _ => {}
            }
        }
    })?;

    // Perform a roundtrip to populate registry
    let cb = display_proxy.sync(&queue)?;
    let synced = Rc::new(RefCell::new(false));
    cb.add_listener({
        let synced = synced.clone();
        move |_, _| {
            *synced.borrow_mut() = true;
        }
    })?;

    // Wait for synchronization to complete
    while !*synced.borrow() {
        display.flush()?;
        let read = display.read(&mut queue)?;
        read.read()?;
    }

    // Bind to the compositor and xdg_wm_base
    let compositor: wl::WlCompositor =
        registry_state
            .borrow()
            .bind(wl::WlCompositor::INTERFACE, 6, &queue)?;
    let xdg_wm_base: xdgs::XdgWmBase =
        registry_state
            .borrow()
            .bind(xdgs::XdgWmBase::INTERFACE, 6, &queue)?;

    // Try to bind to the decoration manager (optional - not all compositors support it)
    let decoration_manager = registry_state
        .borrow()
        .try_bind::<xdg_decoration::ZxdgDecorationManagerV1>(
            xdg_decoration::ZxdgDecorationManagerV1::INTERFACE,
            1,
            &queue,
        );

    if decoration_manager.is_some() {
        println!("Server-side decorations are supported by this compositor");
    } else {
        println!(
            "Server-side decorations are not supported by this compositor - using client-side decorations"
        );
    }

    // Create a surface
    let surface = compositor.create_surface(&queue)?;

    // Create XDG surface and toplevel for window management
    let xdg_surface = xdg_wm_base.get_xdg_surface(&queue, &surface)?;
    let xdg_toplevel = xdg_surface.get_toplevel(&queue)?;

    // Set window title based on decoration support
    let title = if decoration_manager.is_some() {
        "Softbuffer-lite Wayland Rainbow Example with Server-Side Decorations"
    } else {
        "Softbuffer-lite Wayland Rainbow Example"
    };
    xdg_toplevel
        .set_title(CStr::from_bytes_with_nul(format!("{}\0", title).as_bytes()).unwrap())?;

    // Set up decoration if supported
    let decoration_configured = Rc::new(RefCell::new(true)); // Default to true if no decorations
    if let Some(decoration_manager) = decoration_manager {
        // Create decoration object and request server-side decorations
        let decoration = decoration_manager.get_toplevel_decoration(&queue, &xdg_toplevel)?;
        decoration.set_mode(xdg_decoration::zxdg_toplevel_decoration_v1::Mode::SERVER_SIDE)?;

        // Set up decoration listener to handle configure events
        *decoration_configured.borrow_mut() = false; // Wait for decoration configure
        let decoration_configured_clone = decoration_configured.clone();
        decoration.add_listener(move |_, event| {
            if let xdg_decoration::zxdg_toplevel_decoration_v1::Event::Configure { mode } = event {
                println!("Decoration mode configured: {:?}", mode);
                *decoration_configured_clone.borrow_mut() = true;
            }
        })?;
    }

    // Configure the surface
    let width = 800u32;
    let height = 600u32;

    // Set up XDG surface listener for configure events
    let configured = Rc::new(RefCell::new(false));
    {
        let configured_clone = configured.clone();
        xdg_surface.add_listener(move |xdg_surface, event| {
            if let xdgs::xdg_surface::Event::Configure { serial } = event {
                // Acknowledge the configure event
                let _ = xdg_surface.ack_configure(serial);
                *configured_clone.borrow_mut() = true;
            }
        })?;
    }

    // Commit the surface to make it visible
    surface.commit()?;
    display.flush()?;

    // Wait for both the surface configure event and decoration configure event
    while !*configured.borrow() || !*decoration_configured.borrow() {
        let read = display.read(&mut queue)?;
        read.read()?;
    }

    // Create softbuffer-lite context and surface
    let context = Context::new(&display)?;
    let mut softbuffer_surface = context.create_surface(
        &surface,
        NonZeroU32::new(width).unwrap(),
        NonZeroU32::new(height).unwrap(),
    )?;

    // Draw a rainbow pattern
    {
        let mut buffer = softbuffer_surface.acquire()?;
        let buffer_slice = buffer.as_mut();

        // Generate rainbow pattern
        for y in 0..height {
            for x in 0..width {
                let index = (y * width + x) as usize;

                // Create a rainbow pattern based on position
                let hue = (x as f32 / width as f32) * 360.0;
                let saturation = 1.0;
                let value = 1.0 - (y as f32 / height as f32) * 0.5; // Fade from top to bottom

                let rgb = hsv_to_rgb(hue, saturation, value);
                let pixel = (0xFF << 24) | (rgb.0 << 16) | (rgb.1 << 8) | rgb.2;

                if index < buffer_slice.len() {
                    buffer_slice[index] = MaybeUninit::new(pixel);
                }
            }
        }

        // Present the buffer with full damage
        buffer.present()?;
    }

    // Commit the surface to display the buffer
    surface.commit()?;
    display.flush()?;

    // Keep the window open for a few seconds
    std::thread::sleep(std::time::Duration::from_secs(5));

    println!("Rainbow pattern displayed successfully!");
    Ok(())
}

/// Convert HSV color to RGB
fn hsv_to_rgb(h: f32, s: f32, v: f32) -> (u32, u32, u32) {
    let c = v * s;
    let x = c * (1.0 - ((h / 60.0) % 2.0 - 1.0).abs());
    let m = v - c;

    let (r_prime, g_prime, b_prime) = if h < 60.0 {
        (c, x, 0.0)
    } else if h < 120.0 {
        (x, c, 0.0)
    } else if h < 180.0 {
        (0.0, c, x)
    } else if h < 240.0 {
        (0.0, x, c)
    } else if h < 300.0 {
        (x, 0.0, c)
    } else {
        (c, 0.0, x)
    };

    let r = ((r_prime + m) * 255.0) as u32;
    let g = ((g_prime + m) * 255.0) as u32;
    let b = ((b_prime + m) * 255.0) as u32;

    (r, g, b)
}

// MIT/Apache2 License

//! Wayland backend.

use crate::{<PERSON><PERSON><PERSON><PERSON>, EventLoopState, Slab, WakeupC<PERSON><PERSON>, WindowAttributes, WindowId};

use landway::EventQueue;
use landway::protocol::{wayland as wl, xdg_shell as xdgs};
use raw_window_handle::{DisplayHandle, HandleError, HasDisplayHandle, HasWindowHandle, WindowHandle, WaylandWindowHandle};
use rustix::{event, fs as rfs, io as rio, pipe};

use std::cell::RefCell;
use std::io;
use std::marker::PhantomData;
use std::ops::RangeBounds;
use std::os::unix::io::OwnedFd;
use std::rc::Rc;
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use std::task::{Wake, Waker};
use std::time::{Duration, Instant};

/// Wayland display implementation.
pub(crate) struct Display<'f> {
    /// Underlying connection to the server.
    inner: Arc<landway::Display>,

    /// Event queue.
    queue: RefCell<EventQueue>,

    /// Underlying proxy.
    display_proxy: wl::WlDisplay,

    /// Pipe to wake up this display.
    signal: OwnedFd,

    /// Notifier for the signal pipe.
    notifier: (Arc<Notify>, Waker),

    /// Common shared state.
    state: Rc<RefCell<State>>,

    /// Not used by this backend.
    _unused: PhantomData<dyn Fn() + 'f>,
}

/// Owned display handle.
pub(crate) struct OwnedDisplayHandle {
    /// Inner display.
    inner: Arc<landway::Display>,
}

/// Run an event handler.
pub(crate) fn run<'f, H: EventHandler<'f> + 'f>(
    mut display: H::Display,
    mut handler: H,
) -> io::Result<()> {
    macro_rules! display {
        () => {{ std::borrow::Borrow::borrow(&display) }};
    }

    // Perform initialization
    display!().inner.init()?;

    // We wake up the event handler.
    handler.wakeup(&display, WakeupCause::Init);

    loop {
        // Exit if requested.
        if matches!(display!().state.get(), EventLoopState::Exit) {
            break;
        }

        // We are about to start waiting.
        handler.about_to_wait(&display);

        // Wait for new input.
        let user_wake = {
            let display = display!();
            display.inner.dispatch(display.state.get().duration())?
        };

        // Clobber together what woke us up.
        let cause = match (user_wake, display!().state.get()) {
            (_, EventLoopState::Poll) => WakeupCause::Poll,
            (PollEnd::Timeout, _) => WakeupCause::WaitTimedOut,
            _ => WakeupCause::WaitCancelled,
        };
        handler.wakeup(&display, cause);

        if matches!(user_wake, PollEnd::UserWakeup)
            || display!()
                .inner
                .notifier
                .0
                .notified
                .swap(false, Ordering::SeqCst)
        {
            handler.user(&mut display);
        }

        // Drain events.
        let events: Vec<Event> = display!().inner.state.borrow_mut().events.drain(..).collect();
        for event in events {
            match event {
                Event::Sync => {}
                Event::RedrawRequested(window_id) => {
                    handler.redraw_requested(&display, window_id);
                }
            }
        }
    }

    Ok(())
}

impl Display<'_> {
    /// Create a new Wayland display.
    ///
    /// # Errors
    ///
    /// This function returns any errors that occur while `libwayland` is
    /// connecting to the socket.
    pub(crate) fn new() -> io::Result<Self> {
        let inner = landway::Display::connect()?;
        let queue = inner.create_queue(None)?;

        // Open the pipe for notifications.
        let (signal, notifier) = pipe::pipe_with(pipe::PipeFlags::CLOEXEC).or_else(|_| {
            let (signal, notifier) = pipe::pipe()?;
            rio::fcntl_setfd(&signal, rio::fcntl_getfd(&signal)? | rio::FdFlags::CLOEXEC)?;
            rio::fcntl_setfd(
                &notifier,
                rio::fcntl_getfd(&notifier)? | rio::FdFlags::CLOEXEC,
            )?;
            io::Result::Ok((signal, notifier))
        })?;
        rfs::fcntl_setfl(
            &notifier,
            rfs::fcntl_getfl(&notifier)? | rfs::OFlags::NONBLOCK,
        )?;

        // Create the waker used to poll the top-level loop.
        let notifier = Arc::new(Notify {
            notified: AtomicBool::new(false),
            reader: notifier,
        });

        // Common state.
        let state = Rc::new(RefCell::new(State {
            events: Vec::new(),
            registry: None,
            bindings: None,
            windows: Slab::new(),
            synced: false,
        }));

        Ok(Self {
            display_proxy: inner.display_proxy(),
            inner: Arc::new(inner),
            queue: RefCell::new(queue),
            signal,
            notifier: (notifier.clone(), Waker::from(notifier)),
            state,
            _unused: PhantomData,
        })
    }

    /// Create a new window.
    pub(crate) fn create_window(&self, attrs: WindowAttributes<'_>) -> io::Result<WindowId> {
        let mut state = self.state.borrow_mut();
        let bindings = match state.bindings.as_mut() {
            Some(bindings) => bindings,
            None => {
                return Err(io::Error::new(
                    io::ErrorKind::Other,
                    "Wayland display not initialized",
                ));
            }
        };

        // Create the new surface.
        let queue = self.queue.borrow();
        let surface = bindings.compositor.create_surface(&queue)?;
        let mut xdg_surface = bindings.xdg_wm_base.get_xdg_surface(&queue, &surface)?;
        let toplevel = xdg_surface.get_toplevel(&queue)?;
        drop(queue);

        // Set up final state.
        let window_state = WindowState {
            base: surface,
            xdg_surface,
            xdg_toplevel: toplevel,
        };

        // TODO

        // Start tracking it.
        let id = WindowId(state.windows.insert(window_state));
        drop(state);

        // Apply attributes.
        self.apply_attributes(id, attrs)?;

        Ok(id)
    }

    /// Destroy a window.
    pub(crate) fn destroy_window(&self, id: WindowId) -> io::Result<()> {
        let mut state = self.state.borrow_mut();
        let mut window_state = state
            .windows
            .remove(id.0)
            .ok_or_else(|| io::Error::new(io::ErrorKind::NotFound, "Window not found"))?;

        // Destroy the window.
        window_state.xdg_toplevel.destroy()?;
        window_state.xdg_surface.destroy()?;
        window_state.base.destroy()?;

        Ok(())
    }

    /// Request a redraw for the specified window.
    pub(crate) fn request_redraw(&self, window_id: WindowId) -> io::Result<()> {
        let mut state = self.state.borrow_mut();

        // Check if the window exists
        if !state.windows.get(window_id.0).is_some() {
            return Err(io::Error::new(io::ErrorKind::NotFound, "Window not found"));
        }

        // Add a redraw event to the queue
        state.events.push(Event::RedrawRequested(window_id));

        // Wake up the event loop
        self.notifier.1.wake_by_ref();

        Ok(())
    }

    /// Get the window handle for a specific window.
    pub(crate) fn window_handle(&self, window_id: WindowId) -> Result<WindowHandle<'_>, HandleError> {
        // We need to return a window handle, but we can't borrow from the state.
        // Instead, we'll store the surface pointer in the WindowState and recreate the handle.
        let state = self.state.borrow();
        let window_state = state
            .windows
            .get(window_id.0)
            .ok_or(HandleError::Unavailable)?;

        // Get the surface pointer for creating the handle
        let surface_ptr = window_state.surface_ptr;
        drop(state);

        // Create a window handle from the stored pointer
        let handle = WaylandWindowHandle::new(surface_ptr);
        Ok(WindowHandle::borrow_raw(handle.into()))
    }

    /// Get the owned display handle.
    pub(crate) fn create_handle(&self) -> OwnedDisplayHandle {
        OwnedDisplayHandle {
            inner: self.inner.clone(),
        }
    }

    // Maximum API versions.
    const COMPOSITOR_API_MAX: u32 = 6;
    const XDG_WM_API_MAX: u32 = 6;

    /// Bring up all interfaces to the Wayland server.
    fn init(&self) -> io::Result<()> {
        self.init_registry()?;

        // Bind globals.
        let mut state = self.state.borrow_mut();
        if state.bindings.is_some() {
            return Ok(());
        }

        let registry = state.registry.as_mut().unwrap();
        let queue = self.queue.borrow();
        state.bindings = Some(Bindings {
            compositor: registry.bind(
                wl::WlCompositor::INTERFACE,
                1..=Self::COMPOSITOR_API_MAX,
                &queue,
            )?,

            xdg_wm_base: registry.bind(
                xdgs::XdgWmBase::INTERFACE,
                1..=Self::XDG_WM_API_MAX,
                &queue,
            )?,
        });

        Ok(())
    }

    /// Initialize the registry.
    fn init_registry(&self) -> io::Result<()> {
        let mut state = self.state.borrow_mut();
        if state.registry.is_some() {
            return Ok(());
        }

        // Create the proxy.
        let proxy = self.display_proxy.get_registry(&self.queue.borrow())?;

        // Set the registry.
        proxy.add_listener({
            use wl::wl_registry::Event;

            let state = self.state.clone();
            move |_, event| {
                let mut state = state.borrow_mut();
                if let Some(registry) = &mut state.registry {
                    match event {
                        Event::Global {
                            name,
                            interface,
                            version,
                        } => {
                            // Add to globals list.
                            registry.globals.push(Global {
                                name,
                                interface: interface.to_string_lossy().into_owned(),
                                version,
                            });
                        }
                        Event::GlobalRemove { name } => {
                            // Filter out of the list.
                            registry.globals.retain(|global| global.name != name);
                        }
                        _ => {}
                    }
                }
            }
        })?;

        // Set up the registry state.
        state.registry = Some(Registry {
            proxy,
            globals: Vec::new(),
        });

        // Perform a roundtrip to populate registry.
        drop(state);
        self.roundtrip()?;

        Ok(())
    }

    /// Apply attributes to window.
    fn apply_attributes(&self, id: WindowId, attrs: WindowAttributes<'_>) -> io::Result<()> {
        let mut state = self.state.borrow_mut();
        let window = state.windows.get_mut(id.0).unwrap();

        // TODO: Apply attributes

        Ok(())
    }

    /// Force a round-trip.
    fn roundtrip(&self) -> io::Result<()> {
        // Force a synchronization.
        let cb = self.display_proxy.sync(&self.queue.borrow())?;

        // Set up a listener that waits for synchronization to complete.
        self.state.borrow_mut().synced = false;
        cb.add_listener({
            let state = self.state.clone();
            move |_, _| {
                state.borrow_mut().synced = true;
            }
        })?;

        // Wait for the synchronization to complete.
        while !self.state.borrow().synced {
            self.dispatch(None)?;
        }

        Ok(())
    }

    /// Wait for new input.
    ///
    /// Returns `true` if the new event was received by taking out a notification.
    fn dispatch(&self, timeout: Option<Duration>) -> io::Result<PollEnd> {
        // Take out a notification.
        let take_notification = || self.notifier.0.notified.swap(false, Ordering::SeqCst);

        // Flush and then dispatch the existing loop.
        self.inner.flush()?;

        // Dispatch existing events.
        if self.inner.dispatch_queue(&mut self.queue.borrow_mut())? > 0 {
            // We've received new events.
            return Ok(PollEnd::NoUserWakeup);
        }

        // See if we've been notified.
        if take_notification() {
            return Ok(PollEnd::UserWakeup);
        }

        // Begin a new Wayland read.
        let mut queue = self.queue.borrow_mut();
        let read = self.inner.read(&mut queue)?;

        // Poll FD #0: Wayland Display.
        let wayland_fd = event::PollFd::new(&read, event::PollFlags::IN | event::PollFlags::ERR);

        // Poll FD #1: User notification.
        let signal_fd = event::PollFd::new(&self.signal, event::PollFlags::RDNORM);

        // Set up the timer.
        let deadline = timeout.and_then(|timeout| Instant::now().checked_add(timeout));

        // TODO: Timeouts, extra fd sources.
        let mut fds = [wayland_fd, signal_fd];
        loop {
            let time_left = deadline
                .map(|deadline| deadline.saturating_duration_since(Instant::now()))
                .map(|time_left| event::Timespec {
                    tv_sec: time_left.as_secs().try_into().unwrap(),
                    tv_nsec: time_left.subsec_nanos().into(),
                });
            match event::poll(&mut fds, time_left.as_ref()) {
                Ok(_) => break,
                Err(err) if err == rio::Errno::INTR => continue,
                Err(err) => return Err(err.into()),
            }
        }

        let wayland_ev = fds[0].revents();
        let signal_ev = fds[1].revents();
        let mut cause = PollEnd::Timeout;

        // Have we received a new Wayland event?
        let has_wayland = if !wayland_ev.is_empty() {
            read.read()? > 0
        } else {
            false
        };

        // Drain user events if needed.
        if !signal_ev.is_empty() {
            while rio::read(&self.signal, &mut [0; 64]).is_ok() {}
        }

        if has_wayland {
            cause = PollEnd::NoUserWakeup;
        } else if take_notification() {
            cause = PollEnd::UserWakeup;
        }

        Ok(cause)
    }
}

impl HasDisplayHandle for Display<'_> {
    #[inline]
    fn display_handle(&self) -> Result<DisplayHandle<'_>, HandleError> {
        self.inner.display_handle()
    }
}

impl HasDisplayHandle for OwnedDisplayHandle {
    #[inline]
    fn display_handle(&self) -> Result<DisplayHandle<'_>, HandleError> {
        self.inner.display_handle()
    }
}

/// Shared state between the `Display` and all proxies.
struct State {
    /// Events in the queue.
    events: Vec<Event>,

    /// Registry state.
    registry: Option<Registry>,

    /// Bindings.
    bindings: Option<Bindings>,

    /// Map of windows.
    windows: Slab<WindowState>,

    /// Is the synchronization done?
    synced: bool,
}

/// Wayland event in the queue.
enum Event {
    /// Synchronization has completed.
    Sync,

    /// A window should be redrawn.
    RedrawRequested(WindowId),
}

/// Window state.
struct WindowState {
    /// Baseline Wayland surface.
    base: wl::WlSurface,

    /// XDG surface.
    xdg_surface: xdgs::XdgSurface,

    /// XDG toplevel.
    xdg_toplevel: xdgs::XdgToplevel,
}

/// Registry state.
struct Registry {
    /// Registry proxy.
    proxy: wl::WlRegistry,

    /// Globals found by the registry.
    globals: Vec<Global>,
}

impl Registry {
    /// Bind a global in the registry.
    fn bind<I>(
        &mut self,
        interface: &'static landway::Interface,
        version: impl RangeBounds<u32>,
        queue: &EventQueue,
    ) -> io::Result<I>
    where
        landway::Proxy: Into<I>,
    {
        // Set up range bounds.
        let start = match version.start_bound() {
            std::ops::Bound::Included(n) => *n,
            std::ops::Bound::Excluded(n) => n + 1,
            std::ops::Bound::Unbounded => 1,
        };
        let end = match version.end_bound() {
            std::ops::Bound::Included(n) => Some(*n),
            std::ops::Bound::Excluded(n) => Some(n - 1),
            std::ops::Bound::Unbounded => None,
        };

        // Ensure the range is not out of the protocol's maximum range.
        if let Some(end) = end {
            let end = end as i32;
            if end > interface.version() {
                return Err(io::Error::new(
                    io::ErrorKind::InvalidInput,
                    format!(
                        "version {end} is out of range for interface {:?}",
                        interface.name()
                    ),
                ));
            }
        }

        // Bind a global.
        let (name, version) = {
            // Find the global.
            let global = self
                .globals
                .iter()
                .find(|global| global.interface == interface.name().to_string_lossy());

            match global {
                Some(global) => (global.name, global.version),
                None => {
                    return Err(io::Error::new(
                        io::ErrorKind::NotFound,
                        format!("global {:?} not found", interface.name()),
                    ));
                }
            }
        };

        // Ensure it's in range.
        if version < start {
            return Err(io::Error::new(
                io::ErrorKind::InvalidInput,
                format!(
                    "version {version} is out of range for interface {:?}",
                    interface.name()
                ),
            ));
        }

        // Get the version to bind to.
        let version = end.map_or(version, |end| version.min(end));

        // Perform the bind.
        let proxy = self.proxy.bind(name, interface, version, queue)?;
        Ok(proxy.into())
    }
}

/// Bound globals.
struct Bindings {
    /// Wayland compositor.
    compositor: wl::WlCompositor,

    /// XDG window manager base.
    xdg_wm_base: xdgs::XdgWmBase,
}

/// Global Wayland object.
#[derive(Debug)]
struct Global {
    /// ID of the global.
    name: u32,

    /// Interface for the global.
    interface: String,

    /// Version of the global.
    version: u32,
}

/// Notification pipe.
struct Notify {
    /// Are we currently notified?
    notified: AtomicBool,

    /// Read end of the pipe.
    reader: OwnedFd,
}

impl Notify {
    /// Notify the pipe.
    fn notify(&self) {
        if self.notified.swap(true, Ordering::SeqCst) {
            return;
        }

        // Write to the pipe.
        let _ = rio::write(&self.reader, &[1]);
    }
}

impl Wake for Notify {
    #[inline]
    fn wake(self: Arc<Self>) {
        self.notify();
    }

    #[inline]
    fn wake_by_ref(self: &Arc<Self>) {
        self.notify();
    }
}

/// Cause of the wakeup.
#[derive(Clone, Copy)]
enum PollEnd {
    /// The timeout expired.
    Timeout,

    /// We did not get a user wakeup.
    NoUserWakeup,

    /// We got a user wakeup.
    UserWakeup,
}
